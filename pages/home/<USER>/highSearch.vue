<template>
	<view class="high-search">
		<view class="search-wrapper">
			<input class="search-input" @blur="search" type="text" v-model="searchValue" :placeholder="placeholder" />
			<!-- <uni-icons @click="search" class="search-icon" type="search" color="#979797" size="20"></uni-icons> -->
			<image @click="search" class="search-icon"
				src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/search_icon.png" mode="">
			</image>
		</view>
		<view class="district-picker" v-if="showDistrictPicker">
			<picker @change="bindPickerChange" :value="cityIndex" :range="cityList" range-key="name">
				<view class="picker-text">
					<text class="city-name">{{ cityList[cityIndex].name }}</text>
					<image class="down-icon"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png"
						mode=""></image>
				</view>
			</picker>
		</view>
		<slot></slot>
	</view>
</template>
<script>
import mapAPI from '@/common/api/map.js'

export default {
	props: {
		placeholder: {
			type: String,
			default: '请输入'
		},
		showDistrictPicker: {
			type: Boolean,
			default: false
		}
	},
	components: {},
	data() {
		return {
			searchValue: '',
			cityList: [],
			cityIndex: 0
		};
	},
	created() {
		this.loadCityList();
	},
	methods: {
		async loadCityList() {
			try {
				// 调用接口获取数据
				const response = await this.$request.post(mapAPI.getAreaList, {
					data: {}
				});
				
				if (response && response.code === 200 && response.data) {
					this.cityList = this.formatCityData(response.data);
				} else {
					throw new Error('接口返回数据异常');
				}
			} catch (error) {
				console.error('加载城市数据失败:', error);
				// 使用硬编码备选数据
				this.cityList = this.getDefaultCityList();
				uni.showToast({
					title: '数据加载异常，使用本地数据',
					icon: 'none',
					duration: 2000
				});
			}
		},
		formatCityData(apiData) {
			try {
				let formattedList = [];
				
				formattedList.push({ name: '所属市区', code: '' });
				
				if (Array.isArray(apiData)) {
					apiData.forEach(item => {
						if (item && item.name && item.code) {
							formattedList.push({
								name: item.name,
								code: item.code  // 保持接口返回的原始code格式
							});
						}
					});
				}
				
				return formattedList;
			} catch (error) {
				console.error('数据格式转换错误:', error);
				return this.getDefaultCityList();
			}
		},
		getDefaultCityList() {
			return [
				{ name: '所属市区', code: '' }
			];
		},
		search() {
			console.log('this.searchValue', this.searchValue)
			this.$emit('click', {
				name: this.searchValue,
				cityCode: this.cityList[this.cityIndex].code
			})
		},
		bindPickerChange(e) {
			this.cityIndex = e.detail.value;
			this.search();
		}
	}
};
</script>

<style lang="scss" scoped>
.high-search {
	display: flex;
	align-items: center;

	.search-wrapper {
		position: relative;
		flex: 1;
		display: flex;
		margin: 20rpx 20rpx 20rpx 20rpx;
		padding: 0 10rpx 0 20rpx;
		height: 62rpx;
		background: #FFFFFF;
		border-radius: 37rpx 37rpx 37rpx 37rpx;
		border: 1rpx solid #E7E7E7;
		font-family: PingFang SC, PingFang SC;

		.search-input {
			width: 100%;
			height: 100%;
			flex: 1;
			line-height: 62rpx;
		}

		.search-icon {
			width: 33rpx;
			height: 33rpx;
			position: absolute;
			right: 16rpx;
			top: 12rpx;
			z-index: 10
		}
	}

	.district-picker {
		min-width: 200rpx;
		margin:0 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		border-radius: 32rpx;
		padding: 0 20rpx;
		height: 64rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s;
		&:active {
			background-color: #E8E8E8;
			transform: scale(0.98);
			box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
		}
		.picker-text {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;

			.city-name {
				font-size: 28rpx;
				color: #666;
				font-weight: 400;
				max-width: 140rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.down-icon {
				width: 24rpx;
				height: 24rpx;
				margin-left: 8rpx;
				transition: transform 0.3s;
			}
		}
	}
}
</style>