<template>
	<view class="tabbarIndex home">
		<!-- 顶部导航栏信息 -->
		<headerBox :customerInfo="customerInfo" @on-style="nextEleStyle"></headerBox>

		<!-- 广告资讯业务 -->
		<view style="padding: 0 20rpx;" :style="[eleStyle]" v-if="isActivate">
			<adColumn type='1'></adColumn>
		</view>

		<!-- 自定义应用 -->
		<customBussiness ref="customBussiness"></customBussiness>

		<!-- 路况信息 -->
		<roadNotice ref="roadNotice"></roadNotice>

		<!-- 政策宣贯,货运平台 -->
		<platNav></platNav>

		<highServiceTabs></highServiceTabs>
		<!-- 关注公众号提示 -->
		<!-- <attention></attention> -->

		<!-- 在线客服功能 -->
		<customerService></customerService>

		<!-- 平安账号提示框 -->
		<neil-modal :show="modal.show" :auto-close="modal.close" :align="modal.align" :showCancel="modal.showCancel"
			:confirm-text="modal.confirmText" @confirm="remindConfirm">
			<view class="content-wrapper">
				<view class="title">提醒</view>
				<view class="desc" style="margin-bottom: 8rpx;">捷通公司对公充值账户已升级为平安银行账户，您的专属账户信息可在"我的账号"-"对公转账充值账号查询"菜单中查询。
				</view>
				<view class="desc">
					可通过网上银行、手机银行、ATM、银行柜台向此账户转账，充值款预计两小时内到账，无需提供转账回执单。
				</view>
				<checkbox-group @change="remindCheckboxChange">
					<label class="checkbox-wrapper">
						<view>
							<checkbox style="transform:scale(0.7)" value="isRemind" />
						</view>
						<view>不再提示</view>
					</label>
				</checkbox-group>
			</view>
		</neil-modal>

		<!-- 底部导航栏 -->
		<view class="bottom" style="height:120rpx">
			<tabbar current="etcHome" />
		</view>
		<jiHongDialog :showPop.sync="showPop" :result="jiHongResult"></jiHongDialog>
		<!--  #ifdef MP-WEIXIN -->
		<privacyDialog></privacyDialog>
		<!-- 广告 -->
		<!-- <advertisement></advertisement> -->
		<!--  #endif -->

		<!-- 加载框 -->
		<tLoading :isShow="isLoading" />
	</view>
</template>

<script>
	// import {
	// 	defaultAvatar
	// } from './home.js';

	import tLoading from '@/components/common/t-loading.vue'
	import neilModal from '@/components/neil-modal/neil-modal.vue'
	import {
		getTicket,
		getMd5Key,
		getAesKey,
		setCurrUserInfo,
		setOpenid,
		getOpenid,
		getAccountId,
		setAccountId,
		setEtcAccount,
		getLoginUserInfo,
		setDefaultUrl,
	} from '@/common/storageUtil.js'

	import {
		mapGetters,
		mapActions
	} from 'vuex'

	// import attention from './attention';
	import tabbar from "@/components/base/tab-bar/index.vue"
	import headerBox from '../component/header.vue';
	import adColumn from '@/components/home/<USER>'
	// import advertisement from './component/advertisement.vue'
	import customBussiness from '../component/custom-bussiness.vue'
	import platNav from '../component/platform-nav.vue'
	import highServiceTabs from '../component/highServiceTabs.vue'
	import roadNotice from '../component/road-notice.vue'
	import jiHongDialog from '../component/jiHongDialog.vue'
	export default {
		components: {
			neilModal,
			// attention,
			tLoading,
			tabbar,
			headerBox,
			// advertisement,
			adColumn,
			customBussiness,
			platNav,
			highServiceTabs,
			roadNotice,
			jiHongDialog
		},
		data() {
			return {
				isAttention: false,
				isLoading: false,
				topLoading: false,
				isBtnLoader: false,
				isRelative: false,
				vehicleAllDataList: [],
				accountList: [],
				userInfo: {},
				customerInfo: {},
				modal: {
					show: false,
					close: false,
					align: 'center',
					showCancel: false,
					confirmText: '确认'
				},
				remindFlag: [],
				// defaultAvatar,
				eleStyle: {},
				isActivate: true,
				showPop: false, //吉鸿
				jiHongResult: {}, //吉鸿弹窗内容
			}
		},
		computed: {
			isLoginStatus() {
				return getTicket() !== '' && getMd5Key() !== '' && getAesKey() !== ''
			},
			...mapGetters('dict', ['gxCardTypeAllOptions'])
		},
		onLoad(query) {
			//平安账户提醒判断
			if (query.type && query.type == 'login') {
				let closeRemind = '0'
				this.getAccountRemind(closeRemind)
			}
		},
		created() {
			if ((getAesKey() && getMd5Key() && getTicket()) && this.gxCardTypeAllOptions.length == 0) {
				this.$store.dispatch(
					'dict/getAllOptions', {
						businessType: 'CARD_TYPE',
						ruleType: "ALL"
					}
				)
			}
			setDefaultUrl('/pages/home/<USER>/p-home')
		},
		onShow() {
			this.isActivate = true
			if (getTicket()) {
				this.getBindUserInfo()
			}
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
			console.log(this.$refs.customBussiness, 'this.$refs.customBussiness');
			this.$refs.customBussiness.getMenu()
		},
		onHide() {
			this.isActivate = false
		},
		onReady() {},
		onPullDownRefresh() {},
		onShareAppMessage(res) {},

		methods: {
			nextEleStyle(style) {
				this.eleStyle = style;
			},
			vehicleChange(data) {
				this.vehicleAllDataList = data;
			},

			getOpenIdHandle() {
				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}
						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									setOpenid(res.data.openid)

								}

							}
						})
					}
				})
			},
			//获取平安账户列表详情
			getAccountRemind(closeRemind) {
				let data = {
					routePath: this.$interfaces.accountRemind.method,
					bizContent: {
						userNo: getLoginUserInfo().userNo,
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log('res', res)
						if (res.code == 200) {
							if (res.data.isRemind == '0') {
								this.modal.show = true
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			remindCheckboxChange(e) {
				this.remindFlag = e.detail.value
			},
			//平安账户不再提示
			remindConfirm() {
				this.modal.show = false
				if (this.remindFlag.length > 0) {
					//关闭提示
					let data = {
						routePath: this.$interfaces.accountRemind.method,
						bizContent: {
							userNo: getLoginUserInfo().userNo,
							closeRemind: '1'
						}
					}
					this.$request
						.post(this.$interfaces.issueRoute, {
							data: data
						})
						.then((res) => {})
						.catch((error) => {})
				}
			},
			getVerhicleList() {
				if (!getAccountId()) {
					this.isLoading = false
					return
				}
				this.isLoading = true
				setCurrUserInfo({})
				let data = {
					routePath: this.$interfaces.customerBizView.method,
					bizContent: {
						customer_id: getAccountId()
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log('111111', res.dta)
						this.isAttention = true
						this.isLoading = false
						setCurrUserInfo(res.data)
						this.customerInfo = res.data
						if (!res.data.customer_id) return
						this.getAllVerhicleList(res.data.customer_id)
					})
					.catch((error) => {
						this.isLoading = false
					})
			},

			getAllVerhicleList(id) {
				console.log(id, '-----');
				let params = {
					customerId: id
				}
				this.$request
					.post(this.$interfaces.vehicleList, {
						data: params
					})
					.then((res) => {
						if (res.code == 200) {
							this.vehicleAllDataList = res.data || []
						} else {
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
			},
			customerChange() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
			// 我的车辆
			goMyCarHandle() {
				uni.navigateTo({
					url: '/pagesB/vehicleBusiness/vehicleList'
				})
			},

			goLoginHandle() {
				uni.navigateTo({
					url: '/pagesD/login/p-login'
				})
			},

			//计算底部高度
			bottomHeight() {
				uni.getSystemInfo({
					success: (res) => {
						// res - 各种参数
						let top = uni.createSelectorQuery().select('.top')
						top
							.boundingClientRect((data) => {
								this.isRelative = false
								if (
									data &&
									data.height &&
									res.windowHeight - data.height - 63 < 30
								) {
									this.isRelative = true
								}
							})
							.exec()
					}
				})
			},

			//设置登录后的默认账户
			getBindUserInfo() {
				let data = {}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.getEtcAccountList, data)
					.then((res) => {
						console.log(res, '11111')
						if (res.code == 200) {
							this.accountList = []
							this.accountList = res.data || []
							setCurrUserInfo({})
							setEtcAccount({})
							setAccountId()
							let bindItem = this.accountList.filter((item) => {
								if (item.isDefault == '2') {
									return item
								}
							})
							if (bindItem.length != 0) {
								setEtcAccount(bindItem[0])
								setAccountId(bindItem[0].custMastId)
							}
							this.getVerhicleList()
							this.accountId = getAccountId() || ''
							console.log('accountId', this.accountId)
							this.getTermination()
							let countNum = this.accountList.length
						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							})
						}
					})
					.catch((error) => {
						console.log(error, '11111')
						this.isLoading = false
					})
			},
			getTermination() {
				// if (!this.accountId) return
				let data = {
					customerId: this.accountId
				}
				this.$request
					.post(this.$interfaces.jiHongTermination, {
						data: data
					})
					.then((res) => {
						console.log('吉鸿待解约车=========>>>>>>>>', res)
						if (res.code == 200) {
							this.jiHongResult = res.data
							// this.showPop = res.data.hasWaitTermination
							if (res.data.hasWaitTermination) {
								const lastPopupDate = uni.getStorageSync('lastPopupDate');
								const today = this.getCurrentDate();
								// 如果无缓存或缓存日期不是今天，则显示弹窗并更新缓存
								if (!lastPopupDate || lastPopupDate !== today) {
									this.showPop = true;
									uni.setStorageSync('lastPopupDate', today);
								}
							}
						}
					})
					.catch((error) => {
						// uni.showModal({
						// 	title: '提示',
						// 	content: error.msg,
						// 	showCancel: false
						// })
					})
			},
			// 获取当前日期字符串（格式：YYYY-MM-DD）
			getCurrentDate() {
				const date = new Date();
				return [
					date.getFullYear(),
					String(date.getMonth() + 1).padStart(2, '0'),
					String(date.getDate()).padStart(2, '0')
				].join('-');
			},
		}
	}
</script>

<style lang="scss" scoped>
	.content-wrapper {
		.title {
			text-align: left;
			font-size: 30rpx;
			padding: 25rpx 50rpx;
		}

		.desc {
			text-indent: 2em;
			padding: 0 30rpx;
			line-height: 42rpx;
			font-size: 28rpx;
			text-align: left;
		}
	}

	/deep/ .neil-modal__container {
		width: 80%;
	}

	/deep/ .neil-modal__footer-left {
		border-radius: 12rpx;
		background-color: #ffffff;
		border-width: 1rpx;
		border-style: solid;
		border-color: #0066E9;
		color: #0066E9 !important;
		height: 60rpx;
		line-height: 60rpx;
	}

	/deep/ .neil-modal__footer-right {
		border-radius: 12rpx;
		color: #ffffff;
		background-color: #0066E9;
		height: 70rpx;
		line-height: 70rpx;
	}

	.checkbox-wrapper {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>