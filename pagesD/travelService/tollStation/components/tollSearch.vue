<template>
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/search_icon.png" mode=""></image>
      <input type="text" v-model="name" :placeholder="placeholder" class="search-input" confirm-type="search" @confirm="search" />
      <image v-if="name" class="clear-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/clear_icon.png" mode="" @click="clearInput"></image>
    </view>
    <view class="district-picker">
      <picker @change="bindPickerChange" :value="cityIndex" :range="cityList" range-key="name">
        <view class="picker-text">
          <text class="city-name">{{ cityList[cityIndex].name }}</text>
          <image class="down-icon" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png" mode=""></image>
        </view>
      </picker>
    </view>
  </view>
</template>

<script>
import mapAPI from '@/common/api/map.js'

export default {
  props: {
    placeholder: {
      type: String,
      default: '请输入收费站名称'
    }
  },
  data() {
    return {
      name: '',
      cityList: [],
      cityIndex: 0
    };
  },
  created() {
    this.loadCityList();
  },
  methods: {
    async loadCityList() {
      try {
        // 调用接口获取数据
        const response = await this.$request.post(mapAPI.getAreaList, {
          data: {}
        });
        
        if (response && response.code === 200 && response.data) {
          this.cityList = this.formatCityData(response.data);
        } else {
          throw new Error('接口返回数据异常');
        }
      } catch (error) {
        console.error('加载城市数据失败:', error);
        // 使用硬编码备选数据
        this.cityList = this.getDefaultCityList();
        uni.showToast({
          title: '数据加载异常，使用本地数据',
          icon: 'none',
          duration: 2000
        });
      }
    },
    
    formatCityData(apiData) {
      try {
        let formattedList = [];
        
        formattedList.push({ name: '所有市区', code: '' });
        
        if (Array.isArray(apiData)) {
          apiData.forEach(item => {
            if (item && item.name && item.code) {
              formattedList.push({
                name: item.name,
                code: item.code  // 保持接口返回的原始code格式
              });
            }
          });
        }
        
        return formattedList;
      } catch (error) {
        console.error('数据格式转换错误:', error);
        return this.getDefaultCityList();
      }
    },
    
    getDefaultCityList() {
      return [
        { name: '所属市区', code: '' }
      ];
    },
    clearInput() {
      this.name = '';
      this.search();
    },
    search() {
      this.$emit('search', {
        name: this.name,
        shiCode: this.cityList[this.cityIndex].code
      });
    },
    bindPickerChange(e) {
      this.cityIndex = e.detail.value;
      this.search();
    }
  }
};
</script>

<style lang="scss" scoped>
.search-container {
  display: flex;
  padding: 20rpx;
  background-color: #FFFFFF;
  // border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 10;
  border-radius: 8rpx 8rpx 0 0;
  
  .search-box {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    height: 64rpx;
    background: #F5F5F5;
    border-radius: 32rpx;
    padding: 0 24rpx;
    transition: all 0.3s ease;
    
    &:focus-within {
      background: #EAEAEA;
      box-shadow: inset 0 0 4rpx rgba(0, 0, 0, 0.1);
    }
    
    .search-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }
    
    .search-input {
      flex: 1;
      height: 64rpx;
      font-size: 28rpx;
      color: #333333;
    }
    
    .clear-icon {
      width: 32rpx;
      height: 32rpx;
      opacity: 0.7;
      transition: opacity 0.2s ease;
      
      &:active {
        opacity: 1;
      }
    }
  }
  
  .district-picker {
    min-width: 200rpx;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #F5F5F5;
    border-radius: 32rpx;
    padding: 0 20rpx;
    height: 64rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    
    &:active {
      background-color: #E8E8E8;
      transform: scale(0.98);
      box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
    }
    
    .picker-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      
      .city-name {
        font-size: 28rpx;
        color: #666;
        font-weight: 400;
        max-width: 140rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .down-icon {
        width: 24rpx;
        height: 24rpx;
        margin-left: 8rpx;
        transition: transform 0.3s;
      }
    }
  }
}
</style> 