<template>
  <view class="station-detail">
    <!-- 顶部图片区域 -->
    <view class="header">
      <image class="station-image" :src="img || detail.imgurl || getDefaultImage()" mode="aspectFill"></image>
      <view class="station-title">
        <text class="station-name">{{detail.name || name}}</text>
      </view>
    </view>
    
    <!-- 收费站简介 -->
    <view class="intro-section">
      <view class="section-title">收费站简介</view>
      <view class="intro-content">
        {{ detail.description || '暂无简介信息' }}
      </view>

      <!-- 位置信息和导航 -->
      <view class="location-section">
        <view class="location-info">
          <text class="location-text">{{(detail.shiName || '') + (detail.xianName || '')}} | 距您{{ distance | kmFilter }}km</text>
        </view>
        <view class="nav-button" @click="navigate">
          <text class="nav-text">导航</text>
        </view>
      </view>
    </view>
    
    
    <!-- 入口车型及通行时段限制 -->
    <view class="restrictions-section">
      <view class="section-title">入口车型及通行时段限制</view>
      <view class="restrictions-content">
        {{ detail.limitTime || '暂无限制' }}
      </view>
    </view>
    
    <!-- 出入口开关状态 -->
    <view class="status-section">
      <view class="section-title">出入口开关状态</view>
      <view class="status-content">
        <!-- 入口状态 -->
        <view class="status-column entrance-column">
          <view class="status-top">
            <view class="status-label-bg" :class="detail.inOpenCnt > 0 ? 'green' : 'yellow'">
              <text class="status-label">入口</text>
            </view>
            <view class="status-badge" :class="detail.inOpenCnt > 0 ? 'status-normal' : 'status-abnormal'">
              <text class="status-text">{{ detail.inOpenCnt > 0 ? '正常' : '关闭' }}</text>
            </view>
          </view>
          <view class="lane-info">
            <view class="lane-row">
              <text class="lane-label">入口车道总数：</text>
              <text class="lane-number">{{ detail.inTotalCnt || 0 }}</text>
            </view>
            <view class="lane-row">
              <text class="lane-label">入口车道开放数：</text>
              <text class="lane-number">{{ detail.inOpenCnt || 0 }}</text>
            </view>
          </view>
        </view>
        
        <!-- 出口状态 -->
        <view class="status-column exit-column">
          <view class="status-top">
            <view class="status-label-bg" :class="detail.outOpenCnt > 0 ? 'green' : 'yellow'">
              <text class="status-label">出口</text>
            </view>
            <view class="status-badge" :class="detail.outOpenCnt > 0 ? 'status-normal' : 'status-abnormal'">
              <text class="status-text">{{ detail.outOpenCnt > 0 ? '正常' : '关闭' }}</text>
            </view>
          </view>
          <view class="lane-info">
            <view class="lane-row">
              <text class="lane-label">出口车道总数：</text>
              <text class="lane-number">{{ detail.outTotalCnt || 0 }}</text>
            </view>
            <view class="lane-row">
              <text class="lane-label">出口车道开放数：</text>
              <text class="lane-number">{{ detail.outOpenCnt || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 评价入口组件 -->
    <view class="evaluation-section" v-if="detail.id">
      <evaluation-entry
        businessType="tollStation"
        :businessId="detail.id"
        :evaluationCount="evaluationCount"
        :businessName="detail.name || name"
      />
    </view>
  </view>
</template>

<script>
import EvaluationEntry from "@/components/common/evaluation-entry.vue";
import {
  getLoginUserInfo,
  getOpenid,
  getOpenidForRead,
} from '@/common/storageUtil.js'

export default {
  components: {
    EvaluationEntry
  },
  data() {
    return {
      id: '',
      name: '',
      distance: '',
      img: '',
      detail: {},
      evaluationCount: 0
    }
  },
  onLoad(options) {
    if (options) {
      this.id = options.id
      this.name = options.name
      this.img = options.img
      
      // 加载详情数据
      this.getStationDetail(options)
    }
  },
  methods: {
    getDefaultImage() {
      // 如果没有传入图片，基于ID生成默认图片
      if (this.id) {
        const imageIndex = ((parseInt(this.id) % 5) + 1);
        return `https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/tollStation-${imageIndex}.png`;
      }
      return `https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/tollStation-1.png`;
    },
    getStationDetail(options) {
      if (!this.id) {
        uni.showToast({
          title: '参数错误',
          icon: 'none'
        });
        return;
      }

      // 调用真实接口
      this.$request
        .post(this.$interfaces.tollStationDetail, {
          data: { id: this.id }
        })
        .then((res) => {
          console.log('收费站详情数据=====>>>>>', res.data)
          if (res.code == 200 && res.data) {
            this.detail = res.data;
            this.distance = options.distance || this.detail.distance
            this.getEvaluationCount();
          } else {
            uni.showToast({
              title: '获取详情失败',
              icon: 'none'
            });
          }
        })
        .catch((error) => {
          console.error('获取收费站详情失败:', error);
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
        })
    },
    navigate() {
      // 打开地图导航
      uni.openLocation({
        latitude: Number(this.detail.lat || 0),
        longitude: Number(this.detail.lng || 0),
        name: this.detail.name || this.name,
        address: (this.detail.shiName || '') + (this.detail.xianName || '')
      })
    },
    // 获取用户openid
    getUserOpenid() {
      // 尝试多种方式获取openid
      let openid = getOpenid();
      if (!openid) {
        openid = getOpenidForRead();
      }

      // 如果还是没有，尝试从用户信息中获取
      if (!openid) {
        const userInfo = getLoginUserInfo();
        openid = userInfo?.openid || '';
      }

      return openid;
    },
    getEvaluationCount() {
      // 获取收费站评价数量
      if (!this.detail.id) return;
      
      try {
        let params = {
          facilityId: this.detail.id,
          openid: this.getUserOpenid()
        };
        
        // 调用获取收费站评价列表接口，通过data数组长度获取评价数量
        this.$request.post(this.$interfaces.getTollStationEvaluationList, {
          data: params
        }).then(res => {
          if (res.code == 200) {
            this.evaluationCount = res.data.total || 0;
          } else {
            this.evaluationCount = 0;
          }
        }).catch(error => {
          this.evaluationCount = 0;
        });
      } catch (error) {
        this.evaluationCount = 0;
      }
    }
  },
  filters: {
    kmFilter(val) {
      return parseFloat(val || 0).toFixed(1);
    }
  }
}
</script>

<style lang="scss" scoped>
.station-detail {
  background-color: #F7F7F7;
  min-height: 100vh;
  
  .header {
    position: relative;
    width: 100%;
    height: 300rpx;
    
    .station-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .station-title {
      position: absolute;
      top: 46rpx;
      left: 38rpx;
      background: rgba(255,255,255,0.43);
      .station-name {
        font-weight: 600;
        font-size: 36rpx;
        color: #1A2D40;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
      }
    }
  }
  
  .intro-section {
    background-color: #FFFFFF;
    margin-bottom: 20rpx;
    padding: 24rpx;
    border-radius: 16rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      margin-bottom: 20rpx;
    }
    
    .intro-content {
      min-height: 122rpx;
      font-size: 26rpx;
      color: #666666;
      line-height: 1.6;
      background: #F6F6F6;
      padding: 14rpx 20rpx 20rpx 20rpx;
      border-radius: 8rpx;
    }
  }
  
  .location-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #FFFFFF;
    margin-top: 30rpx;
    .location-info {
      flex: 1;
      
      .location-text {
        font-size: 28rpx;
        color: #333333;
      }
    }
    
    .nav-button {
      background: linear-gradient( 270deg, #4F7AF4 0%, #33AFFF 100%);
      padding: 12rpx 32rpx;
      border-radius: 32rpx;
      
      .nav-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
      }
    }
  }
  
  .restrictions-section {
    background-color: #FFFFFF;
    margin-bottom: 20rpx;
    padding: 24rpx;
    border-radius: 16rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .restrictions-content {
      min-height: 122rpx;
      background: #F6F6F6;
      padding: 14rpx 20rpx 20rpx 20rpx;
      border-radius: 8rpx;
      font-size: 26rpx;
      color: #999;
      line-height: 1.6;
    }
  }
  
  .status-section {
    background-color: #FFFFFF;
    margin-bottom: 20rpx;
    padding: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      margin-bottom: 26rpx;
    }
    
    .status-content {
      display: flex;
      gap: 40rpx;
      
      .status-column {
        flex: 1;
        
        .status-top {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          
          .status-label-bg {
            width: 140rpx;
            height: 68rpx;
            background: rgba(8,186,129,0.1);
            text-align: center;
            line-height: 68rpx;
            border-radius: 8rpx 0rpx 0rpx 8rpx;
            &.green {
              background-color: #E8F5E9;
              color: #4CAF50;
            }
            
            &.yellow {
              background: rgba(255,144,0,0.1);
              color: #FF9800;
            }
            
            .status-label {
              font-size: 28rpx;
              font-weight: 400;
            }
          }
          
          .status-badge {
            width: 140rpx;
            height: 68rpx;
            background: #08BA81;
            border-radius: 0rpx 8rpx 8rpx 0rpx;
            text-align: center;
            line-height: 68rpx;
            .status-text {
              font-size: 28rpx;
              font-weight: 500;
              color: #FFFFFF;
            }
            
            &.status-normal {
              background-color: #08BA81;
            }
            
            &.status-abnormal {
              background-color: #FF9000;
            }
          }
        }
        
        .lane-info {
          .lane-row {
            display: flex;
            align-items: baseline;
            margin-bottom: 16rpx;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .lane-label {
              font-size: 28rpx;
              color: #666666;
              margin-right: 8rpx;
            }
            
            .lane-number {
              font-size: 28rpx;
              color: #333333;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
}
</style> 