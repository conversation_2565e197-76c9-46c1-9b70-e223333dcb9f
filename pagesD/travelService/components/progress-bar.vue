<template>
  <cover-view class="progress-wrap">
    <cover-view class="btn" @click.prevent="adjustScale('reduce')">-</cover-view>
    <cover-view
      class="bar blue"
      :style="{ height: blueHeight + 'rpx' }"
    ></cover-view>
    <cover-view class="btn show-btn" :style="{ top: btnHeight + 'rpx' }"
      >+</cover-view
    >
    <cover-view
      class="bar white"
      :style="{ height: whiteHeight + 'rpx' }"
    ></cover-view>

    <cover-view class="btn" @click.prevent="adjustScale('add')">+</cover-view>
  </cover-view>
</template>

<script>
export default {
  props: {
    pageScale: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      blueHeight: 0,
      whiteHeight: 0,
      scale: 0,
      btnHeight: 0
    };
  },
  methods: {
    adjustScale(type) {
      // 定义scale的范围
      const minScale = 3;
      const maxScale = 20;

      // 检查当前scale是否在允许调整的范围之外
      if (
        (type === "add" && this.scale >= maxScale) ||
        (type !== "add" && this.scale <= minScale)
      ) {
        return; // 如果scale已经在边界上，则不进行调整
      }
      if (type == "add") {
        this.scale++;
      } else {
        this.scale--;
      }
      let blueHeight = ((this.scale - 3) / 17) * 260;
      this.whiteHeight = 308 - blueHeight;
      this.blueHeight = blueHeight;
      this.btnHeight = blueHeight + 52;
      console.log(this.scale, blueHeight, this.btnHeight, this.whiteHeight);
      this.$emit("setScaleNum",this.scale)
    }
  },
  created() {
    this.scale = this.pageScale;
    let blueHeight = ((this.scale - 3) / 17) * 260;
    this.whiteHeight = 308 - blueHeight;
    this.blueHeight = blueHeight;
    this.btnHeight = blueHeight + 52;
  }
};
</script>

<style lang="scss" scoped>
.progress-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  .btn {
    width: 52rpx;
    height: 52rpx;
    background: #ffffff;
    text-align: center;
    line-height: 48rpx;
    font-weight: 500;
    box-sizing: border-box;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 1rpx solid #999999;
    font-size: 24rpx;
    color: #999;
    &.show-btn {
      position: absolute;
      left: 0;
      top: 0;
      border: 1rpx solid #4f90ff;
      color: #4f90ff;
      z-index: 8;
    }
  }
  .bar {
    width: 16rpx;
    border: 1rpx solid #999999;
    background: #fff;
    // background: linear-gradient(180deg, #4F90FF 30%, #fff 30%);
    &.blue {
      background: #4f90ff;
    }
    &.white {
    }
  }
}
</style>