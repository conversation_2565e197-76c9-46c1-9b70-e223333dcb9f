<template>
  <cover-view class="info-wrap">
    <!-- 施工 work -->
    <cover-view class="box-wrap" v-if="infoType == 'work'">
      <cover-view class="item-wrap">
        <cover-image
          @click="speek(infoData.description)"
          class="cover-image"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_4.png"
        ></cover-image>

        <cover-view class="text text-info">
          <cover-view class="warn-tip" style="width:112rpx;"
            >涉路施工</cover-view
          >
          <!-- <cover-view class="green-tip">S22</cover-view> -->
          <cover-view class="right-block">
            <cover-view class="black-tip">{{
              infoData.highspeedName
            }}</cover-view>
            <cover-view class="time-tip">{{
              `${infoData.startTime}-${infoData.endTime}`
            }}</cover-view>
          </cover-view>
        </cover-view>
      </cover-view>
      <cover-view class="item-wrap">
        <cover-image
          class="cover-image"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vlocation.png"
        ></cover-image>
        <cover-view class="text" style="width:630rpx;white-space: pre-wrap;">{{
          infoData.description
        }}</cover-view>
      </cover-view>
      <cover-view class="item-wrap">
        <cover-image
          class="cover-image"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/direction.png"
        ></cover-image>
        <cover-view class="text" style="width:100%;word-break: break-all;word-wrap: break-word;">
          方向：{{ infoData.direction
          }}<cover-view class="zhanwei"></cover-view>
        </cover-view>
      </cover-view>
      <cover-view class="item-wrap">
        <cover-image
          class="cover-image"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/vbus.png"
        ></cover-image>
        <cover-view style="width:100%;" class="text text-flow"
          >桩号：{{ `${infoData.startZh}-${infoData.endZh}`
          }}<cover-view class="zhanwei"></cover-view
        ></cover-view>
      </cover-view>
    </cover-view>
    <!-- 路段 road -->
    <cover-view class="box-wrap" v-if="infoType == 'road'">
      <cover-view class="item-wrap">
        <cover-image
          class="cover-image"
          @click="speek(infoData.roadContent)"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_2.png"
        ></cover-image>
        <cover-view class="text text-info">
          <cover-view class="road-tip">优惠路段</cover-view>
          <cover-view class="black-tip title-flow">{{
            infoData.roadName
          }}</cover-view>
        </cover-view>
      </cover-view>
      <cover-view class="gray-wrap">
        <cover-view class="li-wrap">
          <cover-view class="li-label">实施时间</cover-view>
          <cover-view class="li-value">{{ infoData.roadTime }}</cover-view>
        </cover-view>
        <cover-view class="li-wrap">
          <cover-view class="li-label">实施内容</cover-view>
          <cover-view class="li-value" :class="isfoldText ? '' : 'textOpen'">{{
            infoData.roadContent
          }}</cover-view>
        </cover-view>
        <cover-view class="more" @click="foldText">
          <cover-view class="text">{{
            isfoldText ? "展开" : "收起"
          }}</cover-view>
          <cover-image
            class="cover-image"
            :src="
              isfoldText
                ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'
            "
          ></cover-image
        ></cover-view>
      </cover-view>
    </cover-view>
    <!-- 事件 event -->
    <cover-view class="box-wrap" v-if="infoType == 'event'">
      <cover-view class="item-wrap">
        <cover-image
          class="cover-image"
          @click="speek(infoData.description)"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_1.png"
        ></cover-image>

        <cover-view class="text text-info">
          <cover-view
            class="warn-tip event"
            style="width:160rpx;margin-right: 0rpx;"
            >道路事件</cover-view
          >
          <cover-view class="black-tip" style="width: 340rpx;">{{
            infoData.loadName
          }}</cover-view>
          <cover-view class="status-tip">{{
            statusObj[infoData.eventStatus]
          }}</cover-view>
        </cover-view>
      </cover-view>
      <cover-view class="item-wrap">
        <!-- <cover-view class="text" style="margin-right:120rpx;">{{
          infoData.pileNoStart
        }}</cover-view> -->
        <cover-view class="text" style="width:100%;word-break: break-all;word-wrap: break-word;white-space: pre-wrap;">
          方向：{{ infoData.directionTraffic
          }}<cover-view class="zhanwei"></cover-view>
        </cover-view>
      </cover-view>
      <cover-view class="gray-wrap">
        <cover-view class="li-wrap">
          <cover-view
            class="li-value li-value2"
            :class="isfoldText ? 'over-2' : 'textOpen'"
            >{{ infoData.description }}</cover-view
          >
        </cover-view>
        <cover-view class="more" @click="foldText">
          <cover-view class="text">{{
            isfoldText ? "展开" : "收起"
          }}</cover-view>
          <cover-image
            class="cover-image"
            :src="
              isfoldText
                ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'
            "
          ></cover-image
        ></cover-view>
      </cover-view>
      <cover-view class="item-wrap ">
        <cover-view class="text" style="width:100%;"
          >上报时间：{{ infoData.eventTime
          }}<cover-view class="zhanwei"></cover-view
        ></cover-view>
        <!-- <cover-view class="text">{{ infoData.pileNoStart }}</cover-view> -->
      </cover-view>
      <cover-view class="item-wrap">
        <cover-view class="text" style="width:100%;"
          >事件类型：{{ infoData.eventTypeName
          }}<cover-view class="zhanwei"></cover-view
        ></cover-view>
      </cover-view>
    </cover-view>
    <!-- 拥堵 jam -->
    <cover-view class="box-wrap" v-if="infoType == 'jam'">
      <cover-view class="item-wrap" style="margin-bottom:0;">
        <cover-image
          class="cover-image"
          @click="speek(infoData.congestSourceArea)"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/voice_3.png"
        ></cover-image>
        <cover-view class="text text-info">
          <cover-view class="degree">{{
            congestLevel[infoData.congestLevel]
          }}</cover-view>

          <cover-view class="black-tip" style="margin-right:100rpx;">{{
            infoData.roadName
          }}</cover-view>
          <cover-view class="black-tip"
            >拥堵时间{{ infoData.congestDuration }}min</cover-view
          >
        </cover-view>
      </cover-view>
      <cover-view class="item-wrap">
        <cover-view
          class="text"
          style="margin-left:174rpx;font-size: 24rpx;color: #999999;"
          >{{ infoData.startTime }}</cover-view
        >
      </cover-view>
      <cover-view class="gray-wrap">
        <cover-view class="li-wrap">
          <cover-view class="li-label">{{ infoData.direction }}</cover-view>
          <cover-view class="li-value3"
            >拥堵距离 {{ infoData.congestLength }}km</cover-view
          >
        </cover-view>
        <cover-view class="li-wrap">
          <cover-view class="li-label"
            >平均车速 {{ infoData.speed }}km/h</cover-view
          >
          <cover-view class="li-value3">{{
            eventSource[infoData.eventSource]
          }}</cover-view>
        </cover-view>
        <cover-view class="li-wrap">
          <cover-view class="li-label label-text">{{
            infoData.congestTailDesc
          }}</cover-view>
          <cover-view class="li-value li-value3"
            >道路类型 {{ roadType[infoData.roadType] }}</cover-view
          >
        </cover-view>
      </cover-view>
    </cover-view>
    <cover-view class="close" @click="closeInfo">
      <cover-image
        class="cover-image"
        src="@/static/toc/close.png"
      ></cover-image
    ></cover-view>
  </cover-view>
</template>

<script>
const plugin = requirePlugin("WechatSI");
const innerAudioContext = uni.createInnerAudioContext();

export default {
  props: {
    infoType: {
      type: String,
      default: "jam" // event - 事件 work - 施工 jam - 拥堵 road-优惠路段
    },
    infoData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isfoldText: true,
      statusObj: {
        0: "刚上报",
        1: "研判否",
        2: "研判确认",
        3: "处理中",
        10: "处理完成"
      },
      eventSource: {
        1: "疑似拥堵",
        2: "异常拥堵",
        3: "常规拥堵"
      },
      roadType: {
        1: "高速",
        2: "环城及快速路",
        3: "主干",
        4: "次干",
        3: "支干",
        101: "省高速",
        102: "国道",
        103: "省道"
      },
      congestLevel: {
        1: "畅通",
        2: "缓行",
        3: "拥堵",
        4: "严重拥堵"
      }
    };
  },
  methods: {
    foldText() {
      this.isfoldText = !this.isfoldText;
    },
    closeInfo() {
      this.$emit("closeInfo");
    },
    speek(content) {
      let _this = this;
      plugin.textToSpeech({
        lang: "zh_CN",
        tts: true,
        content: content,
        success: function(res) {
          console.log("succ tts", res.filename);
          _this.yuyinPlay(res.filename);
        },
        fail: function(res) {
          console.log("fail tts", res);
        }
      });
    },
    yuyinPlay(src) {
      if (src == "") {
        return;
      }
      // if (innerAudioContext) {
      //   try {
      //     innerAudioContext.pause();
      //     innerAudioContext.destroy();
      //     innerAudioContext = null;
      //   } catch (e) {
      //     //TODO handle the exception
      //     return
      //   }
      // }
      innerAudioContext.autoplay = true;
      innerAudioContext.src = src; //设置音频地址
      innerAudioContext.play(); //播放音频
    }
  }
};
</script>

<style lang="scss" scoped>
.info-wrap {
  width: 100%;
  // height: 188rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  padding: 24rpx 20rpx;
  box-sizing: border-box;
  position: relative;
  .item-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    // &.between {
    //   justify-content: space-between;
    // }
    .cover-image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }
    .text-info {
      // position: relative;
      width: 100%;
      display: flex;
      align-items: center;
      .warn-tip {
        color: #ff9e59;
        margin-right: 30rpx;
        &.event {
          color: #ce6134;
        }
      }
      .road-tip {
        color: #08ba81;
        margin-right: 100rpx;
      }
      .green-tip {
        width: 76rpx;
        height: 36rpx;
        background: #08ba81;
        border-radius: 4rpx 4rpx 4rpx 4rpx;
        color: #ffffff;
        font-weight: 500;
        text-align: center;
        line-height: 36rpx;
      }
      .status-tip {
        width: 114rpx;
        height: 44rpx;
        line-height: 44rpx;
        background: #ff9e59;
        border-radius: 2rpx 2rpx 2rpx 2rpx;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        line-height: 44rpx;
        // margin-left: 60rpx;
        position: absolute;
        right: 60rpx;
      }
      .degree {
        width: 96rpx;
        height: 40rpx;
        line-height: 40rpx;
        font-size: 24rpx;
        text-align: center;
        background: #f82a3c;
        color: #ffffff;
        margin-left: 0;
        margin-right: 32rpx;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        &.yellow {
          background: #ffcc00;
        }
        &.orange {
          background: #ff9000;
        }
        &.green {
          background: #32d3a0;
        }
      }
      .right-block {
        width: 480rpx;
        // margin-left: 112rpx;
      }
      .black-tip {
        // flex: 1;
        color: #333333;
        font-weight: 500;
        word-wrap: break-word;
        white-space: pre-wrap;
        // margin-bottom: 5rpx;
      }
      .time-tip {
        color: #999;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .gray-wrap {
    background: #f6f6f6;
    padding: 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    .li-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      margin-bottom: 16rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .li-label {
        color: #666666;
      }
      .li-value {
        width: 408rpx;
        color: #333333;
        white-space: nowrap; /* 保证文本在一行内显示 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        &.textOpen {
          white-space: pre-wrap;
        }
        &.li-value2 {
          width: 664rpx;
          color: #666666;
        }
        &.li-value3 {
          width: auto;
          color: #333333;
        }
        &.over-2 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          white-space: unset;
        }
      }
    }
    .more {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #999999;
      justify-content: flex-end;
      .cover-image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
  .close {
    position: absolute;
    width: 35rpx;
    height: 35rpx;
    top: 24rpx;
    right: 18rpx;
    z-index: 999;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.text-overflow {
  white-space: nowrap; /* 保证文本在一行内显示 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
}
.text-overflow2 {
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis; //属性规定当文本溢出包含元素时发生的事情  text-overflow: clip|ellipsis|string; (修剪/省略号/指定字符串)
  -webkit-line-clamp: 2;
  /*要显示的行数*/
  /* autoprefixer: off */
  -webkit-box-orient: vertical; //属性规定框的子元素应该被水平或垂直排列
  /* autoprefixer: on */
}
.title-flow {
  // position: absolute;
  right: 45rpx;
  width: 400rpx;
  white-space: pre-wrap;
}
.text-flow {
  white-space: pre-wrap;
}

.zhanwei {
  width: 14rpx;
  height: 5rpx;
}
.label-text {
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
  max-width: 400rpx;
}
</style>