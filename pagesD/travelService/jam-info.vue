<template>
  <view class="jam-info">
    <MapWrapCom :polyline="polyline" :scale="scale" :markers="markers" :mapLocation="mapLocation"
      :includePoints="includePoints" enableTraffic showLocationErrorModal ref="MapWrapCom"
      :height="`calc(100% - ${isIos ? '180' : '120'}rpx)`" @onmarkertap="onmarkertap" @updateLocation="updateLocation"
      @onpolylinetap="onpolylinetap" />
    <!-- 地点搜索 -->
    <AddressSearch class="address" ref="addressSearch" @searchRoute="searchRoute" @truckInfoChange="onTruckInfoChange" />
    <!-- 缩放控制 -->
    <progressBar class="progress-box" :pageScale="scale" @setScaleNum="setScaleNum" />
    <!-- 地图工具 -->
    <mapTool @toolTap="toolTap" class="controls-wrap" pageType="jam" />
    <!-- 底部信息 -->
    <bottomInfo class="info" :class="isIos ? '' : 'isIos'" :infoType="markerInfoType" v-show="showType == 'marker'"
      :infoData="infoData" @closeInfo="closeInfo" />
    <polylineInfo  :class="isIos ? '' : 'isIos'" :lineArr="lineArr" @selectLine="selectLine" :actvieId="activeLineId" @openMapApp="openMapApp"
      v-show="showType == 'polyline'" class="info" />
    <view class="bottom" style="height:120rpx">
      <tabbar current="spread" />
    </view>
  </view>
</template>

<script>
import MapWrapCom from "@/pagesD/components/map/map.vue";
import AddressSearch from "./components/map-search.vue";
import mapTool from "@/pagesD/components/map/map-tool.vue";
import progressBar from "./components/progress-bar.vue";
import bottomInfo from "./components/bottom-info.vue";
import polylineInfo from "./components/polyline-info.vue";
import tabbar from "@/components/base/tab-bar/index.vue";

// 导入工具类和服务
import MapDataService from './utils/mapDataService.js';
import RouteUtils from './utils/routeUtils.js';
import MarkerUtils from './utils/markerUtils.js';
import { DEFAULT_LOCATION } from './utils/mapConstants.js';
export default {
  components: {
    MapWrapCom,
    AddressSearch,
    mapTool,
    progressBar,
    bottomInfo,
    polylineInfo,
    tabbar
  },
  data () {
    return {
      markers: [], // 地图标记点数组
      scale: 15, // 地图缩放级别
      polyline: [], // 路线数组
      latitude: DEFAULT_LOCATION.latitude, // 默认纬度（南宁）
      longitude: DEFAULT_LOCATION.longitude, // 默认经度（南宁）
      dataMarkers: [], // 原始标记数据
      mapLocation: {}, // 地图中心位置
      includePoints: [], // 地图包含的所有点
      lineArr: [], // 路线信息数组
      activeLineId: 0, // 当前选中的路线ID
      drivingPoint: {}, // 导航终点信息
      showType: "", // 显示类型：polyline-路线导航，marker-点信息
      markerInfoType: "road", // 标记信息类型
      infoData: {}, // 标记详细信息
      optionsInfo: null, // 页面参数信息
      truckInfo: null, // 货车信息
      isLoading: false, // 数据加载状态
      // 服务实例
      mapDataService: null,
      routeUtils: null
    };
  },
  created() {
    // 初始化服务实例
    this.mapDataService = new MapDataService(this.$request, this.$interfaces);
    this.routeUtils = new RouteUtils(this.$store);
  },
  computed: {
    isIos () {
      console.log(uni.getSystemInfoSync().platform === "ios", "1211212");
      return uni.getSystemInfoSync().platform === "ios";
    }
  },
  methods: {
    addPolyline (road) {
      let data = road || this.roadData;
      let lineArr = data.map((item, idx) => {
        // console.log(item, "item");
        var coors = JSON.parse(item.polyline);
        let pl = [];
        // //坐标解压（返回的点串坐标，通过前向差分进行压缩）
        // var kr = 10000000;
        // for (var i = 2; i < coors.length; i++) {
        //   coors[i] = Number(coors[i - 2]) + Number(coors[i]) / kr;
        // }
        //将解压后的坐标放入点串数组pl中
        for (var i = 0; i < coors.length; i += 2) {
          pl.push({ latitude: coors[i], longitude: coors[i + 1] });
        }
        let polyline = {
          id: item.id,
          points: pl,
          lately: idx == 0 ? true : false,
          type: "road",
          color: idx == 0 ? "#FDD925" : "#58c16c",
          width: 4,
          borderColor: "#2f693c",
          borderWidth: 1
        };
        // console.log(pl, "plpl");
        let distance =
          item.distance >= 1000000
            ? (item.distance / 1000).toFixed(0)
            : (item.distance / 1000).toFixed(1);
        // 将分钟转换为小时和分钟
        const hours = Math.floor(item.duration / 60); // 取整
        const remainingMinutes = item.duration % 60; // 余数
        let duration = `${hours >= 1 ? hours + "小时" : ""
          }${remainingMinutes}分钟`;
        return {
          lineId: idx,
          polyline,
          distance,
          duration
        };
      });
      lineArr.forEach(item => {
        // 绘制路线
        // console.log(item, 222);
        setTimeout(() => {
          this.polyline.push(item.polyline);
        });
      });
    },
    clearPolyline () {
      this.polyline = this.polyline.filter(
        item => item.level == "abovebuildings"
      );
    },
    setScaleNum (scale) {
      this.scale = scale;
    },
    /**
     * 地图工具点击事件处理
     * @param {Object} item 工具项
     */
    toolTap (item) {
      // 优惠路段功能已屏蔽
      if (item.type === "road") {
        return;
      }
      
      // 处理其他类型的标记显示/隐藏
      this.markers = MarkerUtils.toggleMarkers(this.markers, this.dataMarkers, item.type, item.checkd);
    },
    /**
     * 标记点击事件处理
     * @param {Object} e 事件对象
     */
    onmarkertap (e) {
      const markerId = e.target.markerId;
      
      this.markers = MarkerUtils.handleMarkerClick(
        this.markers, 
        markerId, 
        (clickedMarker) => {
          const result = MarkerUtils.processMarkerClick(clickedMarker);
          
          if (result.action === 'showInfo') {
            this.markerInfoType = result.markerInfoType;
            this.showType = "marker";
            this.infoData = result.infoData;
          }
        }
      );
    },
    onpolylinetap (e) {
      this.roadData.forEach(item => {
        if (item.id == e.target.polylineId) {
          this.markerInfoType = item.type;
          this.infoData = item;
        }
      });
      this.showType = "marker";
      this.selectLineLight(e.target.polylineId);
    },
    selectLineLight (polylineId) {
      this.polyline.forEach(item => {
        if (item.id == polylineId && item.type == "road") {
          console.log(item);
          item.color = "#4F90FF";
        } else {
          if (!item.type) return
          item.color = item.lately ? "#FDD925" : "#58c16c";
        }
      });
    },
    async searchRoute (value) {
      let { name, longitude, latitude } = value.endPoint;
      let location = {
        longitude,
        latitude,
        destination: name
      };
      this.polyline = [];
      this.drivingPoint = location;
      
      // 使用RouteUtils进行路线规划
      const result = await this.routeUtils.planRoute(value);
      
      if (result.success) {
        this.lineArr = JSON.parse(JSON.stringify(result.lineArr));
        this.activeLineId = 0;
        
        // 移除现有的起点终点标记
        this.markers = MarkerUtils.filterRouteMarkers(this.markers);
        
        // 添加新的起点终点标记
        this.markers = MarkerUtils.mergeMarkers(this.markers, result.routeMarkers);
        
        console.log('路线规划成功 - 添加起点终点标记，当前markers数量:', this.markers.length);
        
        // 设置地图包含点
        this.includePoints = result.routeMarkers;
        
        // 绘制路线（逆序以确保第一条路线在最上层）
        const reversedPolylines = [...result.polylines].reverse();
        reversedPolylines.forEach(polyline => {
          this.polyline.push(polyline);
        });
        
        this.showType = "polyline";
      }
    },


    // 打开外部地图
    openMapApp () {
      this.$refs.MapWrapCom.openMapApp(this.drivingPoint);
    },
    updateLocation (location) {
      this.$refs.addressSearch.updateLocation(location);
      this.initMapInfo(location);
    },
    
    /**
     * 选择路线并高亮显示
     * @param {Number} id 路线ID
     */
    selectLine (id) {
      this.activeLineId = id;
      this.polyline = this.routeUtils.selectAndHighlightRoute(this.lineArr, id);
    },
    closeInfo () {
      this.showType = "";
    },




    /**
     * 初始化地图信息，加载各类标记数据
     * @param {Object} location 位置信息
     */
    async initMapInfo (location) {
      if (this.isLoading) return;
      
      this.isLoading = true;
      
      try {
        uni.showLoading({
          title: '地图数据加载中'
        });
        
        // 保存已有的路线标记（起点终点）
        const routeMarkers = MarkerUtils.getRouteMarkers(this.markers);
        
        // 清空数据标记数组，重新加载
        this.dataMarkers = [];
        
        // 重置标记数组，保留路线标记
        this.markers = [...routeMarkers];
        
        // 使用MapDataService批量加载地图数据
        const { markersToShow, markersToStore } = await this.mapDataService.getAllMapData(location);
        
        // 更新标记数组
        this.dataMarkers = markersToStore;
        this.markers = MarkerUtils.mergeMarkers(this.markers, markersToShow);
        
        console.log('数据加载完成，当前markers数量:', this.markers.length);
        
        // 处理页面传入的特定标记信息
        this.handleOptionsInfo();
        
      } catch (err) {
        console.error('地图数据加载失败:', err);
        uni.showToast({
          title: '地图数据加载失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
        uni.hideLoading();
      }
    },

    /**
     * 处理页面传入的选项信息
     */
    handleOptionsInfo () {
      if (!this.optionsInfo) return;
      
      const targetMarker = MarkerUtils.findMarker(
        this.dataMarkers, 
        item => item.id == this.optionsInfo.id
      );
      
      if (targetMarker) {
        // 如果找到目标标记，进行相应处理
        if (targetMarker.type !== "road") {
          this.onmarkertap({
            target: { markerId: targetMarker.id }
          });
        } else {
          this.selectLineLight(targetMarker.id);
        }
        
        this.markerInfoType = targetMarker.type;
        this.infoData = targetMarker;
        this.showType = "marker";
      }
      
      // 设置地图中心位置
      this.setMapLocationFromOptions();
    },

    /**
     * 从选项信息设置地图中心位置
     */
    setMapLocationFromOptions () {
      const { optionsInfo } = this;
      
      if (optionsInfo.lon && optionsInfo.lat) {
        setTimeout(() => {
          this.mapLocation = {
            longitude: optionsInfo.lon,
            latitude: optionsInfo.lat
          };
        }, 1000);
      } else {
        const locationChange = uni.getStorageSync("location");
        if (locationChange) {
          setTimeout(() => {
            this.mapLocation = locationChange;
          }, 1000);
        }
      }
    },
    /**
     * 处理货车信息变更事件
     * @param {Object} truckInfo 货车信息
     */
    onTruckInfoChange(truckInfo) {
      console.log('收到货车信息:', truckInfo);
      this.truckInfo = truckInfo;
      // 可以在这里存储货车信息供其他地方使用，如路线规划时的参数
    }
  },
  onLoad (options) {
    if (options.infoType) {
      this.optionsInfo = {
        infoType: options.infoType,
        id: options.id,
        lon: options.lon,
        lat: options.lat,
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.jam-info {
  position: relative;
  height: 100%;

  .controls-wrap {
    position: absolute;
    right: 24rpx;
    top: 374rpx;
  }

  .address {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }

  .progress-box {
    position: absolute;
    top: 380rpx;
    left: 34rpx;
  }

  .info {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 180rpx;

    &.isIos {
      bottom: 120rpx;
    }
  }
}
</style>